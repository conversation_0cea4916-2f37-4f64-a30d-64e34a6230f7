#include "zexuan/base/mediator.hpp"

#include <algorithm>
#include <format>
#include <iostream>
#include <ranges>

#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"

namespace zexuan {
  namespace base {

    Mediator::Mediator() { std::cout << "Mediator 创建\n"; }

    Mediator::~Mediator() noexcept {
      try {
        ClearAll();
        std::cout << "Mediator 销毁\n";
      } catch (...) {
        // 析构函数中不应该抛出异常
      }
    }

    VoidResult Mediator::EnrollObserver(ObjectId observer_id, std::shared_ptr<Observer> observer) {
      if (!observer) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      std::unique_lock<std::shared_mutex> lock(observer_mutex_);

      // 检查是否已经注册
      if (observer_map_.contains(observer_id)) {
        return std::unexpected(ErrorCode::ALREADY_EXISTS);
      }

      // 注册观察者
      observer_map_[observer_id] = std::move(observer);

      std::cout << std::format("EnrollObserver() 观察者ID {} 注册成功\n", observer_id);
      return {};
    }

    VoidResult Mediator::CancelObserver(ObjectId observer_id) noexcept {
      try {
        std::unique_lock<std::shared_mutex> lock(observer_mutex_);

        auto it = observer_map_.find(observer_id);
        if (it == observer_map_.end()) {
          return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
        }

        observer_map_.erase(it);

        std::cout << std::format("CancelObserver() 观察者ID {} 注销成功\n", observer_id);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::EnrollSubject(ObjectId subject_id, std::shared_ptr<Subject> subject) {
      if (!subject) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      std::unique_lock<std::shared_mutex> lock(subject_mutex_);

      // 检查是否已经注册
      if (subject_map_.contains(subject_id)) {
        return std::unexpected(ErrorCode::ALREADY_EXISTS);
      }

      // 注册目标者
      subject_map_[subject_id] = subject;

      // 添加设备到目标者的映射关系
      auto result = AddDevToSubjectMap(subject);
      if (!result) {
        subject_map_.erase(subject_id);
        return result;
      }

      std::cout << std::format("EnrollSubject() 目标者ID {} 注册成功\n", subject_id);
      return {};
    }

    VoidResult Mediator::CancelSubject(ObjectId subject_id) noexcept {
      try {
        std::unique_lock<std::shared_mutex> lock(subject_mutex_);

        auto it = subject_map_.find(subject_id);
        if (it == subject_map_.end()) {
          return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
        }

        // 移除设备到目标者的映射关系
        RemoveDevToSubjectMap(it->second);

        subject_map_.erase(it);

        std::cout << std::format("CancelSubject() 目标者ID {} 注销成功\n", subject_id);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::SendCommonMsgToSubject(ObjectId subject_id, const CommonMessage& msg,
                                                ObjectId source_id) {
      auto subject = GetSubjectInfoByID(subject_id);
      if (!subject) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      // 调用目标者的推送命令接口
      auto result = subject->PushCommand(msg, source_id);
      if (!result) {
        return std::unexpected(result.error());
      }

      return {};
    }

    VoidResult Mediator::SendCommonMsgToObserver(ObjectId observer_id, const CommonMessage& msg,
                                                 ObjectId source_id) {
      auto observer = GetObserverInfoByID(observer_id);
      if (!observer) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      // 调用观察者的回复结果接口
      auto result = observer->ReplyResult(msg, source_id);
      if (!result) {
        return std::unexpected(result.error());
      }

      return {};
    }

    Result<size_t> Mediator::SendEventMsgToObserver(const EventMessage& msg, ObjectId source_id) {
      size_t success_count = 0;
      size_t total_count = 0;

      std::shared_lock<std::shared_mutex> lock(observer_mutex_);

      // 使用现代 C++ ranges 遍历所有观察者，找到关注此事件的观察者
      auto interested_observers
          = observer_map_ | std::views::values | std::views::filter([&](const auto& observer) {
              return observer && observer->IsCare(msg.event_type, msg.device_uuid);
            });

      for (const auto& observer : interested_observers) {
        total_count++;
        auto result = observer->PushEventNotify(msg, source_id);
        if (result) {
          success_count++;
        }
      }

      std::cout << std::format("事件通知发送完成，成功 {}/{} 个观察者\n", success_count,
                               total_count);
      return success_count;
    }

    Result<ObjectId> Mediator::GetSubjectIdByDevID(DeviceId device_id,
                                                   DeviceCategory device_type) const {
      DeviceUUID device_uuid(device_id, device_type);
      auto subject = GetSubjectInfoByDevID(device_uuid);

      if (!subject) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      ObjectId subject_id = subject->GetObjectId();
      return subject_id;
    }

    Mediator::Statistics Mediator::GetStatistics() const noexcept {
      try {
        Statistics stats;

        {
          std::shared_lock<std::shared_mutex> lock(observer_mutex_);
          stats.observer_count = observer_map_.size();
        }

        {
          std::shared_lock<std::shared_mutex> lock(subject_mutex_);
          stats.subject_count = subject_map_.size();
        }

        {
          std::shared_lock<std::shared_mutex> lock(dev_to_subject_mutex_);
          stats.device_mapping_count = dev_to_subject_map_.size();
        }

        return stats;
      } catch (...) {
        return Statistics{};  // 返回默认值
      }
    }

    VoidResult Mediator::ClearAll() noexcept {
      try {
        // 清理观察者映射
        {
          std::unique_lock<std::shared_mutex> lock(observer_mutex_);
          observer_map_.clear();
        }

        // 清理目标者映射
        {
          std::unique_lock<std::shared_mutex> lock(subject_mutex_);
          subject_map_.clear();
        }

        // 清理设备到目标者映射
        {
          std::unique_lock<std::shared_mutex> lock(dev_to_subject_mutex_);
          dev_to_subject_map_.clear();
        }

        std::cout << "Mediator::ClearAll() 所有注册对象已清理\n";
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    std::shared_ptr<Observer> Mediator::GetObserverInfoByID(ObjectId observer_id) const {
      std::shared_lock<std::shared_mutex> lock(observer_mutex_);
      auto it = observer_map_.find(observer_id);
      return (it != observer_map_.end()) ? it->second : nullptr;
    }

    std::shared_ptr<Subject> Mediator::GetSubjectInfoByID(ObjectId subject_id) const {
      std::shared_lock<std::shared_mutex> lock(subject_mutex_);
      auto it = subject_map_.find(subject_id);
      return (it != subject_map_.end()) ? it->second : nullptr;
    }

    std::shared_ptr<Subject> Mediator::GetSubjectInfoByDevID(const DeviceUUID& device_id) const {
      std::shared_lock<std::shared_mutex> lock(dev_to_subject_mutex_);
      auto it = dev_to_subject_map_.find(device_id);
      return (it != dev_to_subject_map_.end()) ? it->second : nullptr;
    }

    VoidResult Mediator::AddDevToSubjectMap(std::shared_ptr<Subject> subject) {
      if (!subject) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      const auto& reg_info = subject->GetRegObjProperty();
      if (!reg_info.has_devices()) {
        return {};  // 不关注设备，无需添加映射
      }

      try {
        std::unique_lock<std::shared_mutex> lock(dev_to_subject_mutex_);

        // 使用现代 C++ ranges 添加所有管理的设备到映射表
        for (const auto& device : reg_info.devices.value()) {
          dev_to_subject_map_[device] = subject;
        }

        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::RemoveDevToSubjectMap(std::shared_ptr<Subject> subject) noexcept {
      if (!subject) {
        return {};
      }

      try {
        const auto& reg_info = subject->GetRegObjProperty();
        if (!reg_info.has_devices()) {
          return {};  // 不关注设备，无需移除映射
        }

        std::unique_lock<std::shared_mutex> lock(dev_to_subject_mutex_);

        // 使用现代 C++ ranges 移除所有管理的设备映射
        for (const auto& device : reg_info.devices.value()) {
          auto it = dev_to_subject_map_.find(device);
          if (it != dev_to_subject_map_.end() && it->second == subject) {
            dev_to_subject_map_.erase(it);
          }
        }

        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

  }  // namespace base
}  // namespace zexuan
