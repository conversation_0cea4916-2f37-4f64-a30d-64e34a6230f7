#include "zexuan/net/tcp_server.hpp"

#include <spdlog/spdlog.h>
#include <stdio.h>  // snprintf

#include "zexuan/net/tcp_acceptor.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/event_loop_thread_pool.hpp"
#include "zexuan/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::net;

TcpServer::TcpServer(EventLoop* loop, const zexuan::net::Address& listenAddr,
                     const std::string& nameArg, Option option)
    : loop_(loop),
      ipPort_(listenAddr.toIpPort()),
      name_(nameArg),
      acceptor_(new TcpAcceptor(loop, listenAddr, option == kReusePort)),
      threadPool_(new EventLoopThreadPool(loop, name_)),
      connectionCallback_(defaultConnectionCallback),
      messageCallback_(defaultMessageCallback),
      nextConnId_(1) {
  acceptor_->setNewConnectionCallback([this](int sockfd, const zexuan::net::Address& peerAddr) {
    newConnection(sockfd, peerAddr);
  });
}

TcpServer::~TcpServer() {
  loop_->assertInLoopThread();
  spdlog::debug("TcpServer::~TcpServer [{}] destructing", name_);
  for (auto& item : connections_) {
    TcpConnectionPtr conn(item.second);
    item.second.reset();
    conn->getLoop()->runInLoop([conn]() { conn->connectDestroyed(); });
    conn.reset();
  }
}

void TcpServer::setThreadNum(int numThreads) {
  assert(0 <= numThreads);
  threadPool_->setThreadNum(numThreads);
}

void TcpServer::start() {
  if (started_.fetch_add(1) == 0) {
    threadPool_->start(threadInitCallback_);

    assert(!acceptor_->listening());
    loop_->runInLoop([this]() { acceptor_->listen(); });
  }
}

void TcpServer::newConnection(int sockfd, const zexuan::net::Address& peerAddr) {
  loop_->assertInLoopThread();
  EventLoop* ioLoop = threadPool_->getNextLoop();
  char buf[64];
  snprintf(buf, sizeof buf, "-%s#%d", ipPort_.c_str(), nextConnId_);
  ++nextConnId_;
  std::string connName = name_ + buf;
  spdlog::info("TcpServer::newConnection [{}] - new connection [{}] from {}", name_, connName,
               peerAddr.toIpPort());

  zexuan::net::Address localAddr(zexuan::net::sockets::getLocalAddr(sockfd));
  // FIXME poll with zero timeout to double confirm the new connection
  // FIXME use make_shared if necessary
  TcpConnectionPtr conn(new TcpConnection(ioLoop, connName, sockfd, localAddr, peerAddr));
  connections_[connName] = conn;
  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const TcpConnectionPtr& conn) { removeConnection(conn); });
  // called ~TcpConnection()
  ioLoop->runInLoop([conn]() { conn->connectEstablished(); });
}

void TcpServer::removeConnection(const TcpConnectionPtr& conn) {
  // FIXME: unsafe
  loop_->runInLoop([this, conn]() { removeConnectionInLoop(conn); });
}

void TcpServer::removeConnectionInLoop(const TcpConnectionPtr& conn) {
  loop_->assertInLoopThread();
  spdlog::info("TcpServer::removeConnectionInLoop [{}] - connection {}", name_, conn->name());

  size_t n = connections_.erase(conn->name());
  (void)n;
  assert(n == 1);
  EventLoop* ioLoop = conn->getLoop();
  ioLoop->queueInLoop([conn]() { conn->connectDestroyed(); });
}
