#include "zexuan/net/tcp_connector.hpp"

#include <errno.h>
#include <spdlog/spdlog.h>

#include <algorithm>
#include <cassert>
#include <cstring>

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::net;

const int TcpConnector::kMaxRetryDelayMs;

TcpConnector::TcpConnector(EventLoop* loop, const Address& serverAddr)
    : loop_(loop),
      serverAddr_(serverAddr),
      connect_(false),
      state_(kDisconnected),
      retryDelayMs_(kInitRetryDelayMs) {
  spdlog::debug("ctor[{}]", static_cast<void*>(this));
}

TcpConnector::~TcpConnector() {
  spdlog::debug("dtor[{}]", static_cast<void*>(this));
  assert(!channel_);
}

void TcpConnector::start() {
  connect_ = true;
  loop_->runInLoop([this]() { startInLoop(); });
}

void TcpConnector::startInLoop() {
  loop_->assertInLoopThread();
  assert(state_ == kDisconnected);
  if (connect_) {
    connect();
  } else {
    spdlog::debug("do not connect");
  }
}

void TcpConnector::stop() {
  connect_ = false;
  loop_->queueInLoop([this]() { stopInLoop(); });
  // 取消重连定时器
  if (retryTimerId_.valid()) {
    loop_->cancel(retryTimerId_);
    retryTimerId_ = TimerId();
  }
}

void TcpConnector::stopInLoop() {
  loop_->assertInLoopThread();
  if (state_ == kConnecting) {
    setState(kDisconnected);
    int sockfd = removeAndResetChannel();
    retry(sockfd);
  }
}

void TcpConnector::connect() {
  int sockfd = zexuan::net::sockets::createNonblockingOrDie(serverAddr_.family());
  int ret = zexuan::net::sockets::connect(sockfd, serverAddr_.getSockAddr());
  int savedErrno = (ret == 0) ? 0 : errno;
  switch (savedErrno) {
    case 0:
    case EINPROGRESS:
    case EINTR:
    case EISCONN:
      connecting(sockfd);
      break;

    case EAGAIN:
    case EADDRINUSE:
    case EADDRNOTAVAIL:
    case ECONNREFUSED:
    case ENETUNREACH:
      retry(sockfd);
      break;

    case EACCES:
    case EPERM:
    case EAFNOSUPPORT:
    case EALREADY:
    case EBADF:
    case EFAULT:
    case ENOTSOCK:
      spdlog::error("connect error in TcpConnector::startInLoop {}", savedErrno);
      zexuan::net::sockets::close(sockfd);
      break;
  }
}

void TcpConnector::restart() {
  loop_->assertInLoopThread();
  setState(kDisconnected);
  retryDelayMs_ = kInitRetryDelayMs;
  connect_ = true;
  // 取消之前的重连定时器
  if (retryTimerId_.valid()) {
    loop_->cancel(retryTimerId_);
    retryTimerId_ = TimerId();
  }
  startInLoop();
}

void TcpConnector::connecting(int sockfd) {
  setState(kConnecting);
  assert(!channel_);
  channel_.reset(new Channel(loop_, sockfd));
  channel_->setWriteCallback([this]() { handleWrite(); });
  channel_->setErrorCallback([this]() { handleError(); });
  channel_->enableWriting();
}

int TcpConnector::removeAndResetChannel() {
  channel_->disableAll();
  channel_->remove();
  int sockfd = channel_->fd();
  // Can't reset channel_ here, because we are inside Channel::handleEvent
  loop_->queueInLoop([this]() { resetChannel(); });
  return sockfd;
}

void TcpConnector::resetChannel() { channel_.reset(); }

void TcpConnector::handleWrite() {
  spdlog::debug("TcpConnector::handleWrite state={}", static_cast<int>(state_));

  if (state_ == kConnecting) {
    int sockfd = removeAndResetChannel();
    int err = zexuan::net::sockets::getSocketError(sockfd);
    if (err) {
      spdlog::warn("TcpConnector::handleWrite - SO_ERROR = {} {}", err, strerror(err));
      retry(sockfd);
    } else if (zexuan::net::sockets::isSelfConnect(sockfd)) {
      spdlog::warn("TcpConnector::handleWrite - Self connect");
      retry(sockfd);
    } else {
      setState(kConnected);
      if (connect_) {
        newConnectionCallback_(sockfd);
      } else {
        zexuan::net::sockets::close(sockfd);
      }
    }
  } else {
    // what happened?
    assert(state_ == kDisconnected);
  }
}

void TcpConnector::handleError() {
  spdlog::error("TcpConnector::handleError state={}", static_cast<int>(state_));
  if (state_ == kConnecting) {
    int sockfd = removeAndResetChannel();
    retry(sockfd);
  }
}

void TcpConnector::retry(int sockfd) {
  zexuan::net::sockets::close(sockfd);
  setState(kDisconnected);
  if (connect_) {
    spdlog::info("TcpConnector::retry - Retrying connecting to {} in {} milliseconds. ",
                 serverAddr_.toIpPort(), retryDelayMs_);

    // 使用定时器实现延时重连
    retryTimerId_ = loop_->runAfter(retryDelayMs_ / 1000.0, [this]() {
      retryTimerId_ = TimerId();  // 清空定时器ID
      if (connect_) {  // 检查是否仍需要连接
        startInLoop();
      }
    });

    retryDelayMs_ = std::min(retryDelayMs_ * 2, kMaxRetryDelayMs);
  } else {
    spdlog::debug("do not connect");
  }
}
