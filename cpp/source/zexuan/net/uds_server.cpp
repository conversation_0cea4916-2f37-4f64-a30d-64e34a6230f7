#include "zexuan/net/uds_server.hpp"

#include <spdlog/spdlog.h>
#include <stdio.h>  // snprintf

#include "zexuan/net/uds_acceptor.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/event_loop_thread_pool.hpp"
#include "zexuan/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::net;

void defaultUdsConnectionCallback(const UdsConnectionPtr& conn) {
  spdlog::trace("{} -> {} is {}", conn->localPath(), conn->peerPath(),
                (conn->connected() ? "UP" : "DOWN"));
  // do not call conn->forceClose(), because some users want to register message callback only.
}

void defaultUdsMessageCallback(const UdsConnectionPtr&, Buffer* buf, Timestamp) {
  buf->retrieveAll();
}

UdsServer::UdsServer(EventLoop* loop, const std::string& listenPath,
                     const std::string& nameArg, Option option)
    : loop_(loop),
      listenPath_(listenPath),
      name_(nameArg),
      acceptor_(new UdsAcceptor(loop, listenPath)),
      threadPool_(new EventLoopThreadPool(loop, name_)),
      connectionCallback_(defaultUdsConnectionCallback),
      messageCallback_(defaultUdsMessageCallback),
      nextConnId_(1) {
  acceptor_->setNewConnectionCallback([this](int sockfd, const std::string& peerPath) {
    newConnection(sockfd, peerPath);
  });
}

UdsServer::~UdsServer() {
  loop_->assertInLoopThread();
  spdlog::debug("UdsServer::~UdsServer [{}] destructing", name_);
  for (auto& item : connections_) {
    UdsConnectionPtr conn(item.second);
    item.second.reset();
    conn->getLoop()->runInLoop([conn]() { conn->connectDestroyed(); });
    conn.reset();
  }
}

void UdsServer::setThreadNum(int numThreads) {
  assert(0 <= numThreads);
  threadPool_->setThreadNum(numThreads);
}

void UdsServer::start() {
  if (started_.fetch_add(1) == 0) {
    threadPool_->start(threadInitCallback_);

    assert(!acceptor_->listening());
    loop_->runInLoop([this]() { acceptor_->listen(); });
  }
}

void UdsServer::newConnection(int sockfd, const std::string& peerPath) {
  loop_->assertInLoopThread();
  EventLoop* ioLoop = threadPool_->getNextLoop();
  char buf[64];
  snprintf(buf, sizeof buf, "-%s#%d", listenPath_.c_str(), nextConnId_);
  ++nextConnId_;
  std::string connName = name_ + buf;

  spdlog::info("UdsServer::newConnection [{}] - new connection [{}] from {}",
               name_, connName, peerPath);
  
  // 对于UDS，localPath就是listenPath_，peerPath通常为空或客户端路径
  UdsConnectionPtr conn(new UdsConnection(ioLoop, connName, sockfd, listenPath_, peerPath));
  connections_[connName] = conn;
  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const UdsConnectionPtr& conn) { removeConnection(conn); });
  
  ioLoop->runInLoop([conn]() { conn->connectEstablished(); });
}

void UdsServer::removeConnection(const UdsConnectionPtr& conn) {
  // FIXME: unsafe
  loop_->runInLoop([this, conn]() { removeConnectionInLoop(conn); });
}

void UdsServer::removeConnectionInLoop(const UdsConnectionPtr& conn) {
  loop_->assertInLoopThread();
  spdlog::info("UdsServer::removeConnectionInLoop [{}] - connection {}", name_, conn->name());
  size_t n = connections_.erase(conn->name());
  (void)n;
  assert(n == 1);
  EventLoop* ioLoop = conn->getLoop();
  ioLoop->queueInLoop([conn]() { conn->connectDestroyed(); });
}
