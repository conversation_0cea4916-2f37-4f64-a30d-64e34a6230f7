#include "zexuan/base/lifecycle_component.hpp"
#include <gtest/gtest.h>
#include <thread>
#include <chrono>

using namespace zexuan::base;

/**
 * @brief 测试用的生命周期组件实现
 */
class TestLifecycleComponent : public LifecycleComponentBase {
private:
    bool start_called_ = false;
    bool stop_called_ = false;
    bool should_fail_start_ = false;
    bool should_fail_stop_ = false;
    
public:
    explicit TestLifecycleComponent(const std::string& name) 
        : LifecycleComponentBase(name) {}
    
    ~TestLifecycleComponent() override {
        // 确保在析构前调用Stop()
        if (GetState() != ComponentState::STOPPED) {
            Stop();
        }
    }
    
    bool Start() override {
        if (!TransitionTo(ComponentState::STARTING)) {
            return false;
        }
        
        start_called_ = true;
        
        if (should_fail_start_) {
            TransitionTo(ComponentState::ERROR);
            return false;
        }
        
        // 模拟启动过程
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        if (!TransitionTo(ComponentState::RUNNING)) {
            return false;
        }
        
        return true;
    }
    
    bool Stop() override {
        if (GetState() == ComponentState::STOPPED) {
            return true;  // 已经停止
        }
        
        if (!TransitionTo(ComponentState::STOPPING)) {
            return false;
        }
        
        stop_called_ = true;
        
        if (should_fail_stop_) {
            TransitionTo(ComponentState::ERROR);
            return false;
        }
        
        // 模拟停止过程
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        if (!TransitionTo(ComponentState::STOPPED)) {
            return false;
        }
        
        return true;
    }
    
    // 测试辅助方法
    bool IsStartCalled() const { return start_called_; }
    bool IsStopCalled() const { return stop_called_; }
    void SetShouldFailStart(bool fail) { should_fail_start_ = fail; }
    void SetShouldFailStop(bool fail) { should_fail_stop_ = fail; }
};

/**
 * @brief 生命周期组件测试类
 */
class LifecycleComponentTest : public ::testing::Test {
protected:
    void SetUp() override {
        component_ = std::make_unique<TestLifecycleComponent>("TestComponent");
    }
    
    void TearDown() override {
        component_.reset();
    }
    
    std::unique_ptr<TestLifecycleComponent> component_;
};

/**
 * @brief 测试初始状态
 */
TEST_F(LifecycleComponentTest, InitialState) {
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
    EXPECT_EQ(component_->GetComponentName(), "TestComponent");
    EXPECT_FALSE(component_->IsStartCalled());
    EXPECT_FALSE(component_->IsStopCalled());
}

/**
 * @brief 测试正常的启动流程
 */
TEST_F(LifecycleComponentTest, NormalStartFlow) {
    EXPECT_TRUE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    EXPECT_TRUE(component_->IsStartCalled());
}

/**
 * @brief 测试正常的停止流程
 */
TEST_F(LifecycleComponentTest, NormalStopFlow) {
    EXPECT_TRUE(component_->Start());
    EXPECT_TRUE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
    EXPECT_TRUE(component_->IsStopCalled());
}

/**
 * @brief 测试完整的启动-停止周期
 */
TEST_F(LifecycleComponentTest, CompleteLifecycle) {
    // 启动
    EXPECT_TRUE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    
    // 停止
    EXPECT_TRUE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
    
    // 可以重新启动
    EXPECT_TRUE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    
    EXPECT_TRUE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
}

/**
 * @brief 测试重复启动
 */
TEST_F(LifecycleComponentTest, DuplicateStart) {
    EXPECT_TRUE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    
    // 重复启动应该失败
    EXPECT_FALSE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
}

/**
 * @brief 测试重复停止
 */
TEST_F(LifecycleComponentTest, DuplicateStop) {
    EXPECT_TRUE(component_->Start());
    EXPECT_TRUE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
    
    // 重复停止应该成功（幂等操作）
    EXPECT_TRUE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
}

/**
 * @brief 测试启动失败的情况
 */
TEST_F(LifecycleComponentTest, StartFailure) {
    component_->SetShouldFailStart(true);
    EXPECT_FALSE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::ERROR);
}

/**
 * @brief 测试停止失败的情况
 */
TEST_F(LifecycleComponentTest, StopFailure) {
    EXPECT_TRUE(component_->Start());
    
    component_->SetShouldFailStop(true);
    EXPECT_FALSE(component_->Stop());
    EXPECT_EQ(component_->GetState(), ComponentState::ERROR);
}

/**
 * @brief 测试无效的状态转换
 */
TEST_F(LifecycleComponentTest, InvalidStateTransitions) {
    // 从STOPPED状态不能直接转到RUNNING
    EXPECT_EQ(component_->GetState(), ComponentState::STOPPED);
    
    // 启动到RUNNING状态
    EXPECT_TRUE(component_->Start());
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    
    // 从RUNNING状态不能直接转到STARTING
    // 这个测试需要通过内部状态转换来验证，实际使用中不会出现这种情况
}

/**
 * @brief 测试依赖关系（默认实现）
 */
TEST_F(LifecycleComponentTest, Dependencies) {
    auto deps = component_->GetDependencies();
    EXPECT_TRUE(deps.empty());
}

/**
 * @brief 测试析构函数中的状态检查
 */
TEST_F(LifecycleComponentTest, DestructorStateCheck) {
    // 这个测试主要验证析构函数不会崩溃
    // 实际的警告日志需要通过日志系统来验证
    
    auto test_component = std::make_unique<TestLifecycleComponent>("DestructorTest");
    test_component->Start();
    // 不调用Stop()，直接析构，应该在析构函数中输出警告
    test_component.reset();
    
    // 如果程序没有崩溃，说明析构函数处理正确
    SUCCEED();
}

/**
 * @brief 测试多线程环境下的状态转换
 */
TEST_F(LifecycleComponentTest, ConcurrentStateAccess) {
    const int thread_count = 10;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    // 多个线程同时尝试启动
    for (int i = 0; i < thread_count; ++i) {
        threads.emplace_back([this, &success_count]() {
            if (component_->Start()) {
                success_count++;
            }
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    // 只有一个线程应该成功启动
    EXPECT_EQ(success_count.load(), 1);
    EXPECT_EQ(component_->GetState(), ComponentState::RUNNING);
    
    // 清理
    EXPECT_TRUE(component_->Stop());
}
