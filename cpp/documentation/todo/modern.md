# Zexuan C++项目架构重构开发计划书

## 项目概述

### 项目背景
Zexuan C++项目经过多次迭代开发，目前存在架构不统一、生命周期管理混乱、组件内部规范性不足等问题。特别是protocol模块内部结构混乱，各组件的生命周期管理方式不一致，错误处理机制分散，配置管理缺乏统一性。

### 项目目标
1. **建立统一的生命周期管理框架**：确保所有组件都遵循相同的Start/Stop模式，避免析构函数中的不可预测行为
2. **规范化protocol模块**：重构ProtocolGateway、ProtocolService、ProtocolServer等核心组件，建立清晰的职责边界
3. **统一错误处理和日志系统**：所有错误统一使用spdlog记录，建立严格的日志等级规范
4. **统一配置管理**：所有组件配置从./config/config.json读取，移除硬编码参数和默认值

### 项目范围
- **包含**：cpp/include/zexuan和cpp/source/zexuan目录下的所有组件
- **包含**：cpp/app/protocol_server、cpp/app/tcp_bus_server等应用程序
- **暂不包含**：cpp/app/client（后续计划）、dragon、phoenix等其他子项目
- **暂不包含**：代码风格现代化（工作量过大）

## 核心架构设计

### 1. 统一生命周期管理框架

#### 1.1 核心接口定义
```cpp
namespace zexuan::base {

enum class ComponentState {
    STOPPED,        // 已停止（初始状态）
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    ERROR           // 错误状态
};

class ILifecycleComponent {
public:
    virtual ~ILifecycleComponent() = default;

    // 两阶段生命周期管理
    virtual bool Start() = 0;   // 完成所有初始化和启动工作
    virtual bool Stop() = 0;    // 停止所有活动并清理资源

    // 状态查询
    virtual ComponentState GetState() const = 0;
    virtual std::string GetComponentName() const = 0;

    // 依赖管理（可选实现）
    virtual std::vector<std::string> GetDependencies() const { return {}; }
};

}
```

#### 1.2 组件基类实现
```cpp
class LifecycleComponentBase : public ILifecycleComponent {
protected:
    std::atomic<ComponentState> state_{ComponentState::STOPPED};
    std::string component_name_;
    mutable std::mutex state_mutex_;

public:
    explicit LifecycleComponentBase(const std::string& component_name)
        : component_name_(component_name) {
    }

    virtual ~LifecycleComponentBase() {
        if (GetState() != ComponentState::STOPPED) {
            spdlog::warn("{}: Component not stopped before destruction, forcing stop",
                         component_name_);
            Stop();
        }
    }

    ComponentState GetState() const override {
        return state_.load();
    }

    std::string GetComponentName() const override {
        return component_name_;
    }

protected:
    bool TransitionTo(ComponentState new_state) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        ComponentState current = state_.load();

        if (!IsValidTransition(current, new_state)) {
            spdlog::error("{}: Invalid state transition from {} to {}",
                          component_name_, StateToString(current), StateToString(new_state));
            return false;
        }

        state_.store(new_state);
        spdlog::info("{}: State changed to {}", component_name_, StateToString(new_state));
        return true;
    }

private:
    bool IsValidTransition(ComponentState from, ComponentState to) const {
        // 定义合法的状态转换
        switch (from) {
            case ComponentState::STOPPED:
                return to == ComponentState::STARTING;
            case ComponentState::STARTING:
                return to == ComponentState::RUNNING || to == ComponentState::ERROR;
            case ComponentState::RUNNING:
                return to == ComponentState::STOPPING;
            case ComponentState::STOPPING:
                return to == ComponentState::STOPPED || to == ComponentState::ERROR;
            case ComponentState::ERROR:
                return to == ComponentState::STOPPING || to == ComponentState::STOPPED;
            default:
                return false;
        }
    }

    std::string StateToString(ComponentState state) const {
        switch (state) {
            case ComponentState::STOPPED: return "STOPPED";
            case ComponentState::STARTING: return "STARTING";
            case ComponentState::RUNNING: return "RUNNING";
            case ComponentState::STOPPING: return "STOPPING";
            case ComponentState::ERROR: return "ERROR";
            default: return "UNKNOWN";
        }
    }
};
```

#### 1.3 生命周期管理详细规范

**Start阶段必须完成的工作：**
1. 读取配置文件（从传入的config_path参数）
2. 验证配置有效性
3. 创建所有必要的成员对象
4. 分配内存和其他资源
5. 启动所有工作线程
6. 建立网络连接
7. 注册到Mediator（如果需要）
8. 开始处理业务逻辑

**Stop阶段必须完成的工作：**
1. 停止接收新的请求或任务
2. 等待所有正在处理的任务完成（设置合理超时）
3. 从Mediator注销（如果已注册）
4. 关闭所有网络连接
5. 停止所有工作线程（使用join等待）
6. 释放所有分配的资源
7. 清理临时文件和状态

**析构函数规范：**
- 析构函数只能调用Stop()方法
- 不允许在析构函数中进行复杂的清理工作
- 所有复杂清理工作必须在Stop()中完成

### 2. 配置管理统一化

#### 2.1 配置文件结构一览
```json
{
  {
  "spdlog": {
    "level": "debug",
    "pattern": "[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v",
    "max_file_size": 10485760,
    "max_files": 5,
    "flush_on_debug": true,
    "log_directory": "logs"
  },
  "protocol": {
    "server": {
      "listen_address": "0.0.0.0",
      "listen_port": 8080,
      "max_connections": 100,
      "thread_pool_size": 4
    },
    "gateway": {
      "request_timeout_seconds": 30,
      "thread_sleep_ms": 10,
      "max_pending_requests": 1000,
      "protocol_type": "gw104"
    },
    "service": {
      "host": "127.0.0.1",
      "port": 8081
    }
  },
  "bus": {
    "server": {
      "host": "0.0.0.0",
      "port": 8081,
      "thread_pool_size": 4,
      "verbose_logging": true,
      "max_connections": 1000
    }
  },
  "devices": {
    "rename_client": {
      "clientid": 10,
      "thread_pool_size": 32,
      "server": {
        "host": "127.0.0.1",
        "port": 8081
      }
    },
    "tcp_bus_client": {
      "clientid": 9,
      "server": {
        "host": "127.0.0.1",
        "port": 8081
      }
    }
  }
}

```

#### 2.2 配置读取规范
- 所有组件从 `./config/config.json` 读取配置
- 配置文件格式已确定，各组件直接使用现有格式
- 每个组件在Start()阶段读取自己需要的配置项
- 不允许有默认值，所有配置项都必须在config.json中明确定义
- 各组件自行解析JSON配置，无需统一的ConfigManager类

## 详细实施计划

### 阶段一：基础设施建设（优先级：最高）

#### 任务1.1：生命周期管理框架实现
**预期工期：** 3-5个工作日

**具体任务：**
- [ ] 实现ILifecycleComponent接口
- [ ] 实现LifecycleComponentBase基类
- [ ] 实现ComponentState枚举和状态转换逻辑
- [ ] 编写状态转换的单元测试
- [ ] 更新base/CMakeLists.txt添加新文件

**验收标准：**
- 所有状态转换逻辑正确
- 单元测试覆盖率达到90%以上
- 编译无警告和错误

**输出文件：**
- `cpp/include/zexuan/base/lifecycle_component.hpp`
- `cpp/source/zexuan/base/lifecycle_component.cpp`
- `cpp/test/source/test_lifecycle_component.cpp`

#### 任务1.2：配置管理规范化
**预期工期：** 1-2个工作日

**具体任务：**
- [ ] 确认config.json格式符合所有组件需求
- [ ] 验证各组件能正确读取配置
- [ ] 移除代码中的硬编码参数和默认值
- [ ] 编写配置读取的错误处理逻辑

**验收标准：**
- 所有组件都从config.json读取配置
- 配置项缺失时能正确报错并记录日志
- 不存在硬编码的配置参数

**输出文件：**
- 更新各组件的配置读取代码
- 验证config.json的完整性

#### 任务1.3：日志系统规范化
**预期工期：** 1-2个工作日

**具体任务：**
- [ ] 更新所有现有组件使用spdlog::info()等全局API
- [ ] 移除所有logger实例获取和管理代码
- [ ] 移除所有异常抛出，改为日志记录
- [ ] 验证日志格式和等级使用符合规范

**验收标准：**
- 所有组件都直接使用spdlog全局API
- 日志等级使用符合规范文档
- 不再有未捕获的异常
- 不存在logger实例管理代码

**输出文件：**
- 更新各组件的日志调用代码

### 阶段二：Protocol模块重构（优先级：高）

#### 任务2.1：ProtocolGateway重构
**预期工期：** 5-7个工作日

**具体任务：**
- [ ] 继承LifecycleComponentBase基类
- [ ] 重构Start()方法，整合初始化逻辑
- [ ] 重构Stop()方法，确保线程安全停止
- [ ] 从配置文件读取所有参数
- [ ] 统一错误处理和日志记录
- [ ] 重构4个核心线程的启动和停止逻辑

**验收标准：**
- 组件能够正确启动和停止
- 所有配置从config.json读取
- 线程能够安全启动和停止
- 日志记录符合规范

**重点关注：**
- 4个核心线程的生命周期管理
- 队列和条件变量的正确清理
- Mediator的注册和注销时机

#### 任务2.2：ProtocolService重构
**预期工期：** 4-6个工作日

**具体任务：**
- [ ] 继承LifecycleComponentBase基类
- [ ] 重构Start()方法，整合EventLoop和TcpBusClient初始化
- [ ] 重构Stop()方法，确保EventLoop正确停止
- [ ] 从配置文件读取总线连接参数
- [ ] 统一错误处理和日志记录

**验收标准：**
- EventLoop线程能够正确启动和停止
- TcpBusClient连接管理正确
- 所有配置参数从config.json读取
- 组件状态转换正确

#### 任务2.3：ProtocolServer重构
**预期工期：** 4-6个工作日

**具体任务：**
- [ ] 继承LifecycleComponentBase基类
- [ ] 重构Start()方法，整合TcpServer初始化
- [ ] 重构Stop()方法，确保所有连接正确关闭
- [ ] 重构Gateway管理逻辑
- [ ] 从配置文件读取服务器参数
- [ ] 统一错误处理和日志记录

**验收标准：**
- TcpServer能够正确启动和停止
- 所有Gateway能够正确创建和销毁
- 连接管理逻辑清晰
- Service ID分配和回收正确

### 阶段三：应用程序重构（优先级：中）

#### 任务3.1：protocol_server应用重构
**预期工期：** 2-3个工作日

**具体任务：**
- [ ] 重构main函数，使用新的生命周期管理
- [ ] 添加信号处理，确保优雅关闭
- [ ] 统一配置文件路径处理
- [ ] 添加启动和关闭日志

**验收标准：**
- 应用能够正确启动和关闭
- 信号处理正确（SIGINT、SIGTERM）
- 配置文件路径可配置
- 日志记录完整

#### 任务3.2：tcp_bus_server应用重构
**预期工期：** 2-3个工作日

**具体任务：**
- [ ] 重构main函数，使用新的生命周期管理
- [ ] 添加信号处理，确保优雅关闭
- [ ] 统一配置文件路径处理
- [ ] 添加启动和关闭日志

**验收标准：**
- 应用能够正确启动和关闭
- 总线服务功能正常
- 配置管理统一
- 日志记录完整

### 阶段四：测试和验证（优先级：中）

#### 任务4.1：单元测试完善
**预期工期：** 3-4个工作日

**具体任务：**
- [ ] 为所有重构的组件编写单元测试
- [ ] 测试生命周期状态转换
- [ ] 测试配置读取功能
- [ ] 测试错误处理逻辑

**验收标准：**
- 单元测试覆盖率达到80%以上
- 所有测试用例通过
- 测试用例覆盖正常和异常情况

#### 任务4.2：集成测试
**预期工期：** 2-3个工作日

**具体任务：**
- [ ] 编写组件间集成测试
- [ ] 测试完整的消息处理流程
- [ ] 测试系统启动和关闭流程
- [ ] 性能基准测试

**验收标准：**
- 所有集成测试通过
- 系统能够稳定运行
- 性能不低于重构前水平

### 阶段五：文档和部署（优先级：低）

#### 任务5.1：文档更新
**预期工期：** 2-3个工作日

**具体任务：**
- [ ] 更新README文档
- [ ] 编写架构设计文档
- [ ] 更新API文档
- [ ] 编写部署指南

**验收标准：**
- 文档内容准确完整
- 新用户能够根据文档快速上手
- 部署流程清晰

#### 任务5.2：构建系统优化
**预期工期：** 1-2个工作日

**具体任务：**
- [ ] 更新CMakeLists.txt文件
- [ ] 优化编译选项
- [ ] 添加静态分析工具集成
- [ ] 优化依赖管理

**验收标准：**
- 编译速度不低于重构前
- 静态分析无严重问题
- 依赖关系清晰

## 风险评估与应对策略

### 技术风险

**风险1：重构过程中引入新的Bug**
- **概率：** 中等
- **影响：** 高
- **应对策略：**
  - 采用渐进式重构，每次只重构一个组件
  - 完善的单元测试和集成测试
  - 代码审查机制
  - 保留原有代码作为备份

**风险2：生命周期管理复杂度增加**
- **概率：** 低
- **影响：** 中等
- **应对策略：**
  - 详细的状态转换文档
  - 充分的单元测试覆盖
  - 简化的两阶段设计

**风险3：性能回退**
- **概率：** 低
- **影响：** 中等
- **应对策略：**
  - 性能基准测试
  - 关键路径的性能监控
  - 必要时进行性能优化

### 进度风险

**风险1：工作量估算不准确**
- **概率：** 中等
- **影响：** 中等
- **应对策略：**
  - 分阶段实施，每个阶段都有可交付成果
  - 优先实现核心功能
  - 预留缓冲时间

**风险2：依赖组件问题**
- **概率：** 低
- **影响：** 高
- **应对策略：**
  - 明确组件依赖关系
  - 按依赖顺序进行重构
  - 及时沟通和协调

## 人工介入点标识

以下任务需要人工主导完成：

- 👤 **架构设计评审**：需要资深架构师评审生命周期管理框架设计
- 👤 **配置参数确定**：需要业务专家确定各组件的配置项和合理取值范围
- 👤 **性能基准确定**：需要根据实际业务需求确定性能指标和测试标准
- 👤 **测试用例设计**：需要测试专家设计边界条件和异常情况的测试用例
- 👤 **生产环境验证**：需要在实际生产环境中验证重构后的系统稳定性

## 预期收益

### 可维护性提升
- **统一的生命周期管理**：所有组件都遵循相同的启动/停止模式，问题定位更容易
- **清晰的错误处理**：所有错误都有详细的日志记录，故障排查效率提升
- **规范的配置管理**：所有配置集中管理，部署和维护更简单

### 可靠性增强
- **避免析构函数陷阱**：通过明确的Stop()调用，避免析构时的不可预测行为
- **线程安全停止**：确保所有线程都能安全停止，避免程序崩溃
- **资源泄漏防护**：统一的资源管理模式，减少内存和句柄泄漏

### 开发效率提升
- **模块化设计**：清晰的组件边界，便于并行开发和测试
- **统一的接口规范**：新组件开发有明确的模板可以遵循
- **完善的测试体系**：自动化测试减少手工验证工作量

## 总结

本重构计划专注于解决当前项目中最关键的架构问题：生命周期管理不统一、错误处理分散、配置管理混乱。通过建立统一的框架和规范，可以显著提升系统的可维护性和可靠性。

重构将分阶段进行，每个阶段都有明确的交付物和验收标准。优先解决最核心的问题，确保系统在重构过程中始终保持可用状态。

**下一步行动：**
请确认此重构计划是否符合您的预期，如有需要调整的地方请及时反馈。确认后即可开始第一阶段的基础设施建设工作。