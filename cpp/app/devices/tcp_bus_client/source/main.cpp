/**
 * @file main.cpp
 * @brief TCP消息总线客户端测试程序 - 重构版本
 * <AUTHOR>
 * @date 2025-08-25
 */

#include <spdlog/spdlog.h>
#include <iostream>
#include <memory>
#include <string>
#include <atomic>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "../include/tcp_bus_client_app.hpp"

using namespace zexuan::net;
using namespace zexuan::base;
using namespace tcp_bus_client;

// 全局变量
std::atomic<bool> g_running{true};
std::shared_ptr<EventLoop> g_event_loop;
std::unique_ptr<TcpBusClientApp> g_app;

int main(int argc, char* argv[]) {
    // 解析命令行参数
    std::string server_host = "127.0.0.1";
    uint16_t server_port = 8081;
    std::string client_name = "TestClient";
    std::string config_path = "./config/config.json";

    if (argc > 1) {
        server_host = argv[1];
    }
    if (argc > 2) {
        server_port = static_cast<uint16_t>(std::stoi(argv[2]));
    }
    if (argc > 3) {
        client_name = argv[3];
    }
    if (argc > 4) {
        config_path = argv[4];
    }

    // 初始化日志系统
    if (!LoggerManager::initialize("tcp_bus_client.log", config_path)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return -1;
    }

    spdlog::info("TCP Bus Client starting...");
    spdlog::info("Server: {}:{}, Client: {}", server_host, server_port, client_name);

    try {
        // 创建事件循环
        g_event_loop = std::make_shared<EventLoop>();

        // 注册优雅关闭信号
        g_event_loop->registerShutdownSignals([&](int signal) {
            spdlog::info("Received signal {}, shutting down gracefully...", signal);
            g_running.store(false);
            g_event_loop->quit();
        });

        // 创建应用程序实例
        g_app = std::make_unique<TcpBusClientApp>(*g_event_loop, config_path, client_name, 9);

        // 启动应用程序
        if (!g_app->start()) {
            spdlog::error("Failed to start TCP Bus Client App");
            return -1;
        }

        // 运行事件循环（阻塞直到quit()被调用）
        g_event_loop->loop();

        spdlog::info("Event loop stopped");

        // 确保完全清理应用程序
        if (g_app) {
            g_app->stop();
            g_app.reset();
        }

        // 清理事件循环
        g_event_loop.reset();

    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return -1;
    }

    spdlog::info("TCP Bus Client shutdown completed");
    return 0;
}